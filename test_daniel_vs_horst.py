#!/usr/bin/env python3
"""
Test Daniel vs Horst specifically to understand ranking
"""

import sys
import os

# Add current directory to path
sys.path.append(os.getcwd())

def test_daniel_vs_horst():
    """Test Daniel vs Horst ranking"""
    print("=== DANIEL VS HORST ANALYSIS ===")
    
    # Create mock CV content based on what we know
    daniel_cv = """
    Daniel Meixner
    Qualitätsfachkraft / QS-Messtechniker
    
    Berufserfahrung:
    2020-2024: Qualitätssicherung bei Firma ABC
    - Messtechnik und Qualitätskontrolle
    - CNC-Messmaschinen Programmierung
    - Koordinatenmesstechnik (KMG)
    - Qualitätsfachkraft
    
    Kenntnisse:
    - Qualitätssicherung
    - Messtechnik
    - CNC-Messprogramm
    - Koordinatenmesstechnik
    """
    
    horst_cv = """
    Horst Lippert
    
    Berufserfahrung:
    1999-2001: CNC-Kurs bei Bildungseinrichtung
    - Grundlagen CNC-Programmierung
    - Fanuc Steuerung
    
    2001-2020: Verschiedene Tätigkeiten
    - Allgemeine Fertigungsarbeiten
    - Maschinenbedienung
    
    Kenntnisse:
    - CNC-Programmierung (Grundkenntnisse)
    - Fanuc
    - Maschinenbedienung
    """
    
    job_description = """
    CNC Fräser (m/w/d)
    
    Wir suchen einen erfahrenen CNC Fräser für unser Team.
    
    Anforderungen:
    - CNC-Programmierung
    - Fanuc oder Heidenhain Steuerung
    - Zerspanung und Bearbeitung
    - Mindestens 3 Jahre Berufserfahrung
    
    Aufgaben:
    - Programmierung von CNC-Maschinen
    - Rüsten und Einfahren
    - Qualitätskontrolle
    """
    
    try:
        from matcher import CVMatcher
        matcher = CVMatcher()
        
        print("📊 DANIEL MEIXNER ANALYSIS:")
        print("-" * 40)
        daniel_skill_score = matcher.calculate_skill_match(job_description, daniel_cv)
        print(f"Skill Score: {daniel_skill_score:.3f}")
        
        # Check for recent experience
        daniel_recent = '2024' in daniel_cv
        print(f"Has recent experience: {daniel_recent}")
        
        # Check relevant years
        daniel_years = matcher._years_of_relevant_experience(daniel_cv.lower(), [])
        print(f"Relevant years: {daniel_years}")
        
        print("\n📊 HORST LIPPERT ANALYSIS:")
        print("-" * 40)
        horst_skill_score = matcher.calculate_skill_match(job_description, horst_cv)
        print(f"Skill Score: {horst_skill_score:.3f}")
        
        # Check for recent experience
        horst_recent = '2024' in horst_cv
        print(f"Has recent experience: {horst_recent}")
        
        # Check relevant years
        horst_years = matcher._years_of_relevant_experience(horst_cv.lower(), [])
        print(f"Relevant years: {horst_years}")
        
        print("\n🏆 COMPARISON:")
        print("-" * 40)
        print(f"Daniel skill score: {daniel_skill_score:.3f}")
        print(f"Horst skill score:  {horst_skill_score:.3f}")
        
        if daniel_skill_score > horst_skill_score:
            print("✅ Daniel has higher skill score")
        else:
            print("❌ Horst has higher skill score")
            
        print(f"\nDaniel recent experience: {daniel_recent}")
        print(f"Horst recent experience:  {horst_recent}")
        
        print(f"\nDaniel relevant years: {daniel_years}")
        print(f"Horst relevant years:  {horst_years}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_daniel_vs_horst()
