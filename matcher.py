import re
from typing import List, <PERSON><PERSON>
from collections import Counter
import math
from advanced_llm_matcher import EnhancedCVMatcher
from domain_specific_matcher import UniversalDomainMatcher
from job_specific_matcher import JobSpecificMatcher


class CVMatcher:
    def __init__(self, use_enhanced_matching=True, use_domain_specific=True, use_job_specific=True, openai_api_key=None):
        # Common stop words to ignore
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'is', 'are', 'was', 'were', 'be', 'been',
            'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
        }

        # Initialize job-specific matching (highest priority)
        self.use_job_specific = use_job_specific
        if use_job_specific:
            try:
                self.job_specific_matcher = JobSpecificMatcher()
            except Exception as e:
                print(f"Job-specific matcher initialization failed: {e}")
                self.job_specific_matcher = None
                self.use_job_specific = False
        else:
            self.job_specific_matcher = None

        # Initialize domain-specific matching (fallback)
        self.use_domain_specific = use_domain_specific
        if use_domain_specific and not use_job_specific:
            try:
                self.domain_matcher = UniversalDomainMatcher()
            except Exception as e:
                print(f"Universal domain matcher initialization failed: {e}")
                self.domain_matcher = None
                self.use_domain_specific = False
        else:
            self.domain_matcher = None

        # Initialize enhanced matching if requested
        self.use_enhanced_matching = use_enhanced_matching
        if use_enhanced_matching and not use_domain_specific and not use_job_specific:
            try:
                self.enhanced_matcher = EnhancedCVMatcher(openai_api_key)
            except Exception as e:
                print(f"Enhanced matcher initialization failed: {e}")
                self.enhanced_matcher = None
                self.use_enhanced_matching = False
        else:
            self.enhanced_matcher = None

    def preprocess_text(self, text: str) -> List[str]:
        """Preprocess text by cleaning and tokenizing"""
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters and keep only alphanumeric and spaces
        text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
        
        # Split into words
        words = text.split()
        
        # Remove stop words and short words
        words = [word for word in words if word not in self.stop_words and len(word) > 2]
        
        return words

    def calculate_tf_idf_similarity(self, job_description: str, cv_content: str) -> float:
        """Calculate TF-IDF based similarity with multilingual improvements"""
        # Preprocess texts
        job_words = self.preprocess_text(job_description)
        cv_words = self.preprocess_text(cv_content)

        # Add synonym expansion for better matching
        synonyms = {
            'developer': ['entwickler', 'programmer', 'programmierer'],
            'experience': ['erfahrung', 'expertise'],
            'software': ['software', 'anwendung'],
            'project': ['projekt', 'vorhaben'],
            'team': ['team', 'gruppe'],
            'management': ['management', 'führung', 'leitung'],
            'database': ['datenbank', 'db'],
            'web': ['web', 'internet', 'online'],
            'programming': ['programmierung', 'entwicklung'],
            'skills': ['fähigkeiten', 'kenntnisse']
        }

        # Expand vocabulary with synonyms
        enhanced_job_words = job_words.copy()
        enhanced_cv_words = cv_words.copy()

        for word in job_words:
            if word in synonyms:
                enhanced_job_words.extend(synonyms[word])

        for word in cv_words:
            if word in synonyms:
                enhanced_cv_words.extend(synonyms[word])

        # Create vocabulary from enhanced word lists
        all_words = set(enhanced_job_words + enhanced_cv_words)

        if not all_words:
            return 0.1  # Give a small base score

        # Calculate term frequencies
        job_tf = Counter(enhanced_job_words)
        cv_tf = Counter(enhanced_cv_words)

        # Calculate similarity using cosine similarity
        dot_product = 0
        job_magnitude = 0
        cv_magnitude = 0

        for word in all_words:
            job_freq = job_tf.get(word, 0)
            cv_freq = cv_tf.get(word, 0)

            dot_product += job_freq * cv_freq
            job_magnitude += job_freq ** 2
            cv_magnitude += cv_freq ** 2

        if job_magnitude == 0 or cv_magnitude == 0:
            return 0.05  # Very low score for no overlap

        similarity = dot_product / (math.sqrt(job_magnitude) * math.sqrt(cv_magnitude))

        # Don't artificially boost the score - use natural cosine similarity
        # Only apply modest scaling to make scores more meaningful
        scaled_similarity = similarity * 1.5  # Modest boost instead of 3.0

        return min(1.0, scaled_similarity)  # Cap at 1.0

    def calculate_keyword_match(self, job_description: str, cv_content: str) -> float:
        """Calculate keyword-based matching score with improved algorithm"""
        job_words = set(self.preprocess_text(job_description))
        cv_words = set(self.preprocess_text(cv_content))

        if not job_words:
            return 0.0

        # Calculate intersection
        intersection = job_words.intersection(cv_words)

        # Use a more conservative scoring method
        # Focus on how many job requirements are met
        job_coverage = len(intersection) / len(job_words) if job_words else 0

        # Add bonus for domain-specific important keywords
        job_lower = job_description.lower()
        cv_lower = cv_content.lower()

        # Detect job type and apply appropriate keyword importance
        if any(term in job_lower for term in ['cnc', 'zerspanung', 'fräsen', 'drehen']):
            # Manufacturing job - prioritize manufacturing keywords
            important_keywords = {
                'cnc', 'programmierung', 'zerspanung', 'fräsen', 'drehen', 'rüsten',
                'fanuc', 'heidenhain', 'qualität', 'messtechnik', 'maschinen'
            }
        else:
            # Software/general job
            important_keywords = {
                'developer', 'engineer', 'programmer', 'analyst', 'manager',
                'entwickler', 'ingenieur', 'programmierer', 'analyst', 'manager',
                'experience', 'erfahrung', 'skills', 'fähigkeiten',
                'project', 'projekt', 'team', 'software'
            }

        important_matches = len(intersection.intersection(important_keywords))
        importance_bonus = important_matches * 0.08  # Reduced bonus per important keyword

        # Combine coverage with importance bonus
        final_score = job_coverage + importance_bonus

        return min(1.0, final_score)  # Cap at 1.0

    def _years_of_relevant_experience(self, cv_txt: str, skills_hit: List[str]) -> int:
        """
        Sum year-spans only when the same text block also contains a hit on a core skill.
        This prevents counting irrelevant experience years.
        """
        import re
        from datetime import datetime

        total = 0
        now = datetime.now().year

        # Define core CNC skills that indicate relevant experience
        core_cnc_skills = ['cnc', 'fanuc', 'heidenhain', 'machining', 'fräsen', 'drehen', 'programmieren']

        # Find all year ranges in the CV
        year_pattern = r'(\d{4})-(\d{4}|\bheute\b|\bpresent\b)'
        year_matches = re.finditer(year_pattern, cv_txt, re.IGNORECASE)

        for match in year_matches:
            start_year_str = match.group(1)
            end_year_str = match.group(2)

            # Get the text block around this year range (±200 characters)
            start_pos = max(0, match.start() - 200)
            end_pos = min(len(cv_txt), match.end() + 200)
            text_block = cv_txt[start_pos:end_pos].lower()

            # Check if this text block contains any core CNC skills
            has_relevant_skill = any(skill in text_block for skill in core_cnc_skills)

            if has_relevant_skill:
                start_year = int(start_year_str)
                if end_year_str.lower() in ['heute', 'present']:
                    end_year = now
                else:
                    end_year = int(end_year_str)

                # Only count positive year spans
                if end_year > start_year:
                    total += end_year - start_year

        return total

    def calculate_skill_match(self, job_description: str, cv_content: str) -> float:
        """Calculate skill-specific matching score with precise requirement matching"""

        # Define skill mappings with exact matches and synonyms (case-insensitive)
        skill_mappings = {
            # Programming Languages (with German context)
            'java': ['java', 'java8', 'java11', 'java17', 'openjdk', 'mit java', 'java und', 'kenntnisse in java'],
            'python': ['python', 'python3', 'django', 'flask', 'fastapi'],
            'javascript': ['javascript', 'js', 'typescript', 'node.js', 'nodejs'],
            'c++': ['c++', 'cpp', 'c plus plus'],
            'c#': ['c#', 'csharp', 'c sharp', '.net'],

            # Frameworks (with German context)
            'spring': ['spring', 'spring boot', 'springboot', 'spring framework', 'mit spring boot', 'java und spring boot'],
            'react': ['react', 'reactjs', 'react.js'],
            'angular': ['angular', 'angularjs'],
            'vue': ['vue', 'vue.js', 'vuejs'],

            # Databases (with German context)
            'postgresql': ['postgresql', 'postgres', 'psql', 'postgresql-datenbanken', 'mit postgresql', 'wie postgresql'],
            'mysql': ['mysql', 'mariadb'],
            'mongodb': ['mongodb', 'mongo'],
            'oracle': ['oracle', 'oracle db'],
            'sql': ['sql', 'structured query language'],

            # DevOps & Tools (with German context)
            'git': ['git', 'github', 'gitlab', 'bitbucket', 'nutzung von git', 'erfahrung mit git'],
            'jenkins': ['jenkins', 'ci/cd', 'continuous integration', 'git und jenkins'],
            'docker': ['docker', 'containerization', 'containers'],
            'kubernetes': ['kubernetes', 'k8s', 'container orchestration'],

            # Methodologies (with German context)
            'scrum': ['scrum', 'agile', 'sprint', 'scrum-team', 'agilen scrum-team', 'agilen methoden', 'scrum)'],
            'kanban': ['kanban', 'lean'],
            'rest': ['rest', 'restful', 'rest api', 'api', 'rest-services', 'entwicklung von rest-services', 'rest-apis'],

            # German technical terms
            'softwareentwicklung': ['software development', 'software engineering', 'softwareentwicklerin', 'softwareentwickler'],
            'webentwicklung': ['web development', 'web programming', 'webanwendungen', 'webanwendung'],
            'datenbank': ['database', 'db', 'datenbanken', 'relationalen datenbanken'],
            'programmierung': ['programming', 'coding'],
            'berufserfahrung': ['experience', 'work experience', 'professional experience'],

            # CNC & Manufacturing Skills - CRITICAL for manufacturing jobs
            'cnc_machining_programming': ['cnc-programmierung', 'cnc programmierung', 'programmierung von cnc-maschinen', 'cnc-programmierkenntnisse', 'cnc programmierkenntnisse'],
            'cnc_measurement_programming': ['cnc-messprogramm', 'programmierung von cnc-messmaschinen', 'cnc messprogramm'],
            'fanuc': ['fanuc', 'fanuc steuerung', 'fanuc cnc'],
            'heidenhain': ['heidenhain', 'heidenhain steuerung', 'heidenhain cnc'],
            'siemens_cnc': ['siemens', 'siemens cnc', 'sinumerik'],
            'machining': ['zerspanung', 'bearbeitung', 'mechanische bearbeitung', 'spanende bearbeitung', 'fräsen', 'drehen', 'nc-fräsen', 'nc-drehen'],
            'cnc_operation': ['cnc-maschinen', 'cnc maschinen', 'maschinenbedienung', 'maschinenbetreuung', 'cnc-bedienung', 'cnc-fachkraft'],
            'quality_control': ['qualitätskontrolle', 'qualitätssicherung', 'qs', 'messtechnik', 'qualitätsfachkraft'],
            'measurement_tech': ['messtechnik', 'messtechniker', 'qs-messtechniker', 'koordinatenmesstechnik', 'kmg'],
            'precision_mechanics': ['feinwerkmechaniker', 'präzisionsmechanik', 'feinmechanik'],
            'industrial_mechanics': ['industriemechaniker', 'industrielle mechanik'],
            'machinist': ['zerspanungsmechaniker', 'zerspaner', 'maschinenbediener'],
            'metal_processing': ['metallverarbeitung', 'metallbearbeitung', 'metallverarbeitend'],
            'process_optimization': ['prozessoptimierung', 'verfahrensoptimierung'],
            'machine_setup': ['rüsten', 'einrichten', 'maschineneinrichtung', 'werkzeugeinstellung'],
            'cam_systems': ['cam', 'cam-systeme', 'cam programmierung'],
            'cad_systems': ['cad', 'cad-systeme', 'konstruktion'],
            'aerospace': ['aerospace', 'luftfahrt', 'luft- und raumfahrt'],
            'defense': ['defence', 'defense', 'verteidigung', 'rüstung'],
        }

        # Education keywords (comprehensive with German context)
        education_keywords = {
            'computer science': [
                'computer science', 'informatik', 'cs degree', 'bachelor informatik', 'master informatik',
                'b.sc. informatik', 'm.sc. informatik', 'bachelor of science informatik',
                'master of science informatik', 'diplom informatik', 'studium der informatik',
                'abgeschlossenes studium der informatik', 'informatikstudium'
            ],
            'software engineering': ['software engineering', 'software entwicklung'],
            'information technology': ['information technology', 'it', 'informationstechnik'],
            'engineering': ['engineering', 'ingenieur', 'ingenieurwesen'],
            'qualification': ['qualifikation', 'vergleichbare qualifikation', 'qualification', 'degree'],

            # Manufacturing & Technical Education (NEW)
            'mechanical_engineering': ['maschinenbau', 'mechanical engineering', 'ingenieur maschinenbau'],
            'manufacturing_engineering': ['fertigungstechnik', 'produktionstechnik', 'manufacturing engineering'],
            'precision_mechanics_training': ['feinwerkmechaniker', 'ausbildung feinwerkmechaniker'],
            'industrial_mechanics_training': ['industriemechaniker', 'ausbildung industriemechaniker'],
            'machinist_training': ['zerspanungsmechaniker', 'ausbildung zerspanungsmechaniker'],
            'technical_degree': ['techniker', 'staatlich geprüfter techniker', 'fachschule'],
            'vocational_training': ['berufsausbildung', 'ausbildung', 'lehre', 'abgeschlossene berufsausbildung'],
            'metal_processing_training': ['metallverarbeitend', 'metallbearbeitung', 'metallverarbeitender beruf'],
        }

        # Experience patterns (enhanced for German CVs)
        experience_patterns = [
            r'(\d+)\+?\s*(?:years?|jahre?)\s*(?:of\s*)?(?:experience|erfahrung)',
            r'(\d+)-(\d+)\s*(?:years?|jahre?)',
            r'(?:experience|erfahrung)\s*:?\s*(\d+)\+?\s*(?:years?|jahre?)',
            r'mindestens\s+(\d+)\s+jahre?\s+berufserfahrung',  # German: "mindestens X Jahre Berufserfahrung"
            r'(\d+)\s+jahre?\s+berufserfahrung',  # German: "X Jahre Berufserfahrung"
            r'(\d{4})-(\d{4})',  # Year ranges like 2018-2020
            r'(\d{4})-heute',    # German "until today"
            r'(\d{4})-present',  # English "until present"
        ]

        job_lower = job_description.lower()
        cv_lower = cv_content.lower()

        # Extract required skills from job description
        required_skills = []
        for skill, synonyms in skill_mappings.items():
            for synonym in synonyms:
                if synonym in job_lower:
                    required_skills.append(skill)
                    break

        # Extract candidate skills from CV
        candidate_skills = []
        for skill, synonyms in skill_mappings.items():
            for synonym in synonyms:
                if synonym in cv_lower:
                    candidate_skills.append(skill)
                    break

        # Check education requirements
        education_match = 0
        for edu_type, keywords in education_keywords.items():
            if any(keyword in job_lower for keyword in keywords):
                if any(keyword in cv_lower for keyword in keywords):
                    education_match = 1
                    break

        # Check experience requirements
        import re
        from datetime import datetime
        experience_match = 0
        required_years = 0
        candidate_years = 0

        # Extract required years from job description
        for pattern in experience_patterns[:3]:  # Only use explicit year patterns for requirements
            matches = re.findall(pattern, job_lower)
            if matches:
                if isinstance(matches[0], tuple):
                    required_years = int(matches[0][0])
                else:
                    required_years = int(matches[0])
                break

        # Extract candidate years from CV - calculate from year ranges
        candidate_years = self._years_of_relevant_experience(cv_lower, candidate_skills)

        # Also check for explicit experience mentions
        for pattern in experience_patterns[:3]:
            matches = re.findall(pattern, cv_lower)
            if matches:
                if isinstance(matches[0], tuple):
                    explicit_years = int(matches[0][1])
                else:
                    explicit_years = int(matches[0])
                candidate_years = max(candidate_years, explicit_years)
                break

        if candidate_years >= required_years and required_years > 0:
            experience_match = 1
        elif required_years == 0:
            experience_match = 0.5  # No specific requirement
        elif candidate_years > 0:
            # Partial credit for some experience
            experience_match = min(candidate_years / required_years, 1.0)

        # Add recency bonus/penalty
        recency_bonus = 1.0 if re.search(r'2023|2024', cv_lower) else 0.7
        experience_match = experience_match * recency_bonus

        # Detect job type for better scoring
        job_type = self._detect_job_type(job_lower)

        # Calculate skill match score with domain-specific weighting
        if not required_skills:
            skill_score = 0.1  # Very low score if no specific skills mentioned
        else:
            matched_skills = set(required_skills).intersection(set(candidate_skills))
            base_skill_score = len(matched_skills) / len(required_skills)

            # Apply STRICT domain-specific requirements
            if job_type == 'manufacturing':
                # Hard core-skill gate for CNC jobs (include CNC measurement programming as core skill)
                core_cnc_skills = ['cnc_machining_programming', 'cnc_measurement_programming', 'fanuc', 'heidenhain', 'machining', 'cnc_operation']
                has_core_cnc_skill = any(skill in matched_skills for skill in core_cnc_skills)

                if not has_core_cnc_skill:
                    # No mandatory CNC skill → heavy downgrade
                    return 0.10  # Very low score for missing core skills

                # For CNC/manufacturing jobs, distinguish between different types of CNC experience
                direct_cnc_skills = ['cnc_machining_programming', 'machining', 'cnc_operation', 'fanuc', 'heidenhain']
                measurement_cnc_skills = ['cnc_measurement_programming']
                general_manufacturing_skills = ['precision_mechanics', 'industrial_mechanics', 'machinist', 'metal_processing', 'quality_control']

                # Check what type of CNC experience the candidate has
                direct_cnc_matches = [skill for skill in matched_skills if skill in direct_cnc_skills]
                measurement_cnc_matches = [skill for skill in matched_skills if skill in measurement_cnc_skills]
                general_manufacturing_matches = [skill for skill in matched_skills if skill in general_manufacturing_skills]

                if direct_cnc_matches:
                    # Direct CNC machining experience - highest priority for CNC jobs
                    cnc_bonus = len(direct_cnc_matches) * 0.35  # Very high bonus for direct CNC
                    skill_score = min(1.0, base_skill_score + cnc_bonus)
                elif measurement_cnc_matches and any(term in job_lower for term in ['programmieren', 'rüsten', 'einfahren']):
                    # CNC measurement programming - relevant but lower priority for machining jobs
                    measurement_bonus = len(measurement_cnc_matches) * 0.20  # Moderate bonus
                    skill_score = min(1.0, base_skill_score + measurement_bonus)
                elif general_manufacturing_matches:
                    # General manufacturing experience - some relevance
                    manufacturing_bonus = len(general_manufacturing_matches) * 0.10  # Small bonus
                    skill_score = min(1.0, base_skill_score + manufacturing_bonus)
                else:
                    # NO manufacturing experience = very low score
                    skill_score = base_skill_score * 0.2  # Massive penalty
            else:
                skill_score = base_skill_score

        # Weighted final score with job-type specific adjustments
        if job_type == 'manufacturing':
            # For manufacturing jobs: Skills 75%, Experience 20%, Education 5%
            # (Recent hands-on CNC practice > everything else)
            final_score = (skill_score * 0.75) + (experience_match * 0.20) + (education_match * 0.05)
        else:
            # For software jobs: Skills 60%, Education 20%, Experience 20%
            final_score = (skill_score * 0.6) + (education_match * 0.2) + (experience_match * 0.2)

        return min(1.0, final_score)

    def _detect_job_type(self, job_description_lower: str) -> str:
        """Detect the type of job based on keywords in the job description"""

        # Manufacturing/CNC job indicators
        manufacturing_indicators = [
            'cnc', 'zerspanung', 'bearbeitung', 'maschinen', 'fräser', 'dreher',
            'zerspanungsmechaniker', 'industriemechaniker', 'feinwerkmechaniker',
            'metallverarbeitung', 'qualitätskontrolle', 'messtechnik', 'fanuc',
            'heidenhain', 'programmieren', 'rüsten', 'einfahren', 'prozessoptimierung',
            'serienproduktion', 'bemusterung', 'maschinenbetreuung'
        ]

        # Software development job indicators
        software_indicators = [
            'software', 'entwicklung', 'programmierung', 'java', 'python', 'javascript',
            'spring', 'react', 'angular', 'database', 'datenbank', 'web', 'api',
            'git', 'jenkins', 'scrum', 'agile'
        ]

        # Count indicators
        manufacturing_count = sum(1 for indicator in manufacturing_indicators if indicator in job_description_lower)
        software_count = sum(1 for indicator in software_indicators if indicator in job_description_lower)

        # Determine job type
        if manufacturing_count > software_count:
            return 'manufacturing'
        elif software_count > manufacturing_count:
            return 'software'
        else:
            return 'general'  # Default for unclear cases

    def calculate_match_score(self, cv_path: str, job_description: str) -> float:
        """Calculate overall match score between CV and job description"""
        try:
            # Extract text from CV
            from cv_extractor import CVDataExtractor
            extractor = CVDataExtractor()
            cv_content = extractor.extract_text_from_file(cv_path)

            if not cv_content:
                return 0.0

            # Try job-specific matching first (highest priority)
            if self.use_job_specific and self.job_specific_matcher:
                try:
                    job_requirements = self.job_specific_matcher.extract_job_requirements(job_description)
                    evaluation = self.job_specific_matcher.evaluate_candidate(cv_content, job_requirements)

                    # Convert to percentage (max possible score is 15*3 + 10 + 5 = 60)
                    max_possible = len(job_requirements.skill_set) * 3 + 10 + 5
                    percentage = (evaluation.total_score / max_possible) * 100 if max_possible > 0 else 0
                    return min(100, percentage)
                except Exception as e:
                    print(f"Job-specific matching failed, falling back to domain-specific: {e}")

            # Try domain-specific matching (fallback)
            if self.use_domain_specific and self.domain_matcher:
                try:
                    job_requirements = self.domain_matcher.extract_job_requirements(job_description)
                    candidate_profile = self.domain_matcher.evaluate_candidate(cv_content, job_requirements)
                    scores = self.domain_matcher.calculate_conservative_score(
                        candidate_profile, job_requirements, cv_content
                    )
                    return scores['final_score']
                except Exception as e:
                    print(f"Domain-specific matching failed, falling back to traditional: {e}")

            # Try enhanced matching if available
            elif self.use_enhanced_matching and self.enhanced_matcher:
                try:
                    enhanced_score = self.enhanced_matcher.calculate_enhanced_match_score(
                        job_description, cv_content
                    )
                    return enhanced_score
                except Exception as e:
                    print(f"Enhanced matching failed, falling back to traditional: {e}")

            # Traditional matching approach (fallback)
            # Calculate different similarity metrics
            tf_idf_score = self.calculate_tf_idf_similarity(job_description, cv_content)
            keyword_score = self.calculate_keyword_match(job_description, cv_content)
            skill_score = self.calculate_skill_match(job_description, cv_content)

            # Detect job type for adaptive scoring
            job_type = self._detect_job_type(job_description.lower())

            # Adaptive weighted combination based on job type
            if job_type == 'manufacturing':
                # For manufacturing: Skills 60%, Keywords 25%, TF-IDF 15%
                # (Skills matter most for hands-on technical work)
                overall_score = (skill_score * 0.6) + (keyword_score * 0.25) + (tf_idf_score * 0.15)
            else:
                # For software/general: Skills 50%, Keywords 30%, TF-IDF 20%
                overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)

            # Convert to percentage
            return overall_score * 100

        except Exception as e:
            print(f"Error calculating match score: {e}")
            return 0.0

    def calculate_match_score_from_content(self, job_description: str, cv_content: str) -> float:
        """Calculate match score directly from CV content (for database-stored CVs)"""
        try:
            if not cv_content:
                return 0.0

            # Try job-specific matching first (highest priority)
            if self.use_job_specific and self.job_specific_matcher:
                try:
                    job_requirements = self.job_specific_matcher.extract_job_requirements(job_description)
                    evaluation = self.job_specific_matcher.evaluate_candidate(cv_content, job_requirements)

                    # Convert to percentage (max possible score is 15*3 + 10 + 5 = 60)
                    max_possible = len(job_requirements.skill_set) * 3 + 10 + 5
                    percentage = (evaluation.total_score / max_possible) * 100 if max_possible > 0 else 0
                    return min(100, percentage)
                except Exception as e:
                    print(f"Job-specific matching failed, falling back to domain-specific: {e}")

            # Try domain-specific matching (fallback)
            if self.use_domain_specific and self.domain_matcher:
                try:
                    job_requirements = self.domain_matcher.extract_job_requirements(job_description)
                    candidate_profile = self.domain_matcher.evaluate_candidate(cv_content, job_requirements)
                    scores = self.domain_matcher.calculate_conservative_score(
                        candidate_profile, job_requirements, cv_content
                    )
                    return scores['final_score']
                except Exception as e:
                    print(f"Domain-specific matching failed, falling back to traditional: {e}")

            # Try enhanced matching if available
            elif self.use_enhanced_matching and self.enhanced_matcher:
                try:
                    enhanced_score = self.enhanced_matcher.calculate_enhanced_match_score(
                        job_description, cv_content
                    )
                    return enhanced_score
                except Exception as e:
                    print(f"Enhanced matching failed, falling back to traditional: {e}")

            # Traditional matching approach (fallback)
            # Calculate different similarity metrics
            tf_idf_score = self.calculate_tf_idf_similarity(job_description, cv_content)
            keyword_score = self.calculate_keyword_match(job_description, cv_content)
            skill_score = self.calculate_skill_match(job_description, cv_content)

            # Detect job type for adaptive scoring
            job_type = self._detect_job_type(job_description.lower())

            # Adaptive weighted combination based on job type
            if job_type == 'manufacturing':
                # For manufacturing: Skills 60%, Keywords 25%, TF-IDF 15%
                overall_score = (skill_score * 0.6) + (keyword_score * 0.25) + (tf_idf_score * 0.15)
            else:
                # For software/general: Skills 50%, Keywords 30%, TF-IDF 20%
                overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)

            # Convert to percentage
            return overall_score * 100

        except Exception as e:
            print(f"Error calculating match score from content: {e}")
            return 0.0

    def match(self, job_description: str, cv_contents: List[str]) -> List[Tuple[float, str]]:
        """Match multiple CVs against a job description and return sorted results"""
        results = []
        
        for i, cv_content in enumerate(cv_contents):
            # Calculate different similarity metrics
            tf_idf_score = self.calculate_tf_idf_similarity(job_description, cv_content)
            keyword_score = self.calculate_keyword_match(job_description, cv_content)
            skill_score = self.calculate_skill_match(job_description, cv_content)
            
            # Detect job type for adaptive scoring
            job_type = self._detect_job_type(job_description.lower())

            # Adaptive weighted combination based on job type
            if job_type == 'manufacturing':
                # For manufacturing: Skills 60%, Keywords 25%, TF-IDF 15%
                overall_score = (skill_score * 0.6) + (keyword_score * 0.25) + (tf_idf_score * 0.15)
            else:
                # For software/general: Skills 50%, Keywords 30%, TF-IDF 20%
                overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
            
            results.append((overall_score, cv_content))
        
        # Sort by score in descending order
        results.sort(key=lambda x: x[0], reverse=True)
        
        return results

    def get_match_explanation(self, job_description: str, cv_content: str) -> dict:
        """Get detailed explanation of match score"""
        tf_idf_score = self.calculate_tf_idf_similarity(job_description, cv_content)
        keyword_score = self.calculate_keyword_match(job_description, cv_content)
        skill_score = self.calculate_skill_match(job_description, cv_content)
        
        overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
        
        # Find common keywords
        job_words = set(self.preprocess_text(job_description))
        cv_words = set(self.preprocess_text(cv_content))
        common_keywords = job_words.intersection(cv_words)
        
        return {
            'overall_score': overall_score * 100,
            'tf_idf_score': tf_idf_score * 100,
            'keyword_score': keyword_score * 100,
            'skill_score': skill_score * 100,
            'common_keywords': list(common_keywords)[:10],  # Top 10 common keywords
            'explanation': f"Overall match: {overall_score*100:.1f}% "
                          f"(Content similarity: {tf_idf_score*100:.1f}%, "
                          f"Keyword match: {keyword_score*100:.1f}%, "
                          f"Skill match: {skill_score*100:.1f}%)"
        }
